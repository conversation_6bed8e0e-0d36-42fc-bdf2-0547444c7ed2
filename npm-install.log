npm verbose cli /home/<USER>/.nvm/versions/node/v24.0.2/bin/node /home/<USER>/.nvm/versions/node/v24.0.2/bin/npm
npm info using npm@11.3.0
npm info using node@v24.0.2
npm verbose title npm install
npm verbose argv "install" "--loglevel" "verbose"
npm verbose logfile logs-max:10 dir:/home/<USER>/.npm/_logs/2025-07-30T14_29_43_759Z-
npm verbose logfile /home/<USER>/.npm/_logs/2025-07-30T14_29_43_759Z-debug-0.log
npm http fetch GET 200 https://registry.npmjs.org/next 1589ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@opentelemetry%2fapi 202ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/react 220ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/react-dom 249ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/sass 200ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@types%2fnode 281ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@types%2freact 184ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@types%2freact-dom 179ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/eslint 179ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/eslint-config-next 209ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/typescript 281ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/csstype 173ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/espree 211ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/ignore 155ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/debug 655ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/levn 666ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/chalk 672ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@types%2fprop-types 710ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/ajv 721ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/esquery 178ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@types%2fscheduler 748ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/esutils 246ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/find-up 242ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/globals 236ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/doctrine 201ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/graphemer 204ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/is-glob 242ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/js-yaml 222ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/minimatch 249ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/optionator 247ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@eslint%2fjs 263ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/text-table 276ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/strip-ansi 282ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/cross-spawn 280ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/glob-parent 333ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/fast-deep-equal 166ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/is-path-inside 180ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/imurmurhash 251ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/natural-compare 178ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/lodash.merge 244ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/eslint-scope 264ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@eslint%2feslintrc 171ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/file-entry-cache 201ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/escape-string-regexp 199ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/eslint-visitor-keys 210ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@eslint-community%2fregexpp 216ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@humanwhocodes%2fconfig-array 213ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@nodelib%2ffs.walk 252ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@eslint-community%2feslint-utils 189ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/eslint-plugin-jsx-a11y 213ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/json-stable-stringify-without-jsonify 253ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/eslint-plugin-import 231ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/eslint-plugin-react 256ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@rushstack%2feslint-patch 242ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@humanwhocodes%2fmodule-importer 283ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@next%2feslint-plugin-next 284ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/postcss 164ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/eslint-plugin-react-hooks 216ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/busboy 244ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/eslint-import-resolver-node 262ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/eslint-import-resolver-typescript 256ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@typescript-eslint%2fparser 321ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/watchpack 247ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/graceful-fs 179ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/styled-jsx 185ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@next%2fenv 363ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/caniuse-lite 251ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@swc%2fhelpers 319ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@next%2fswc-darwin-arm64 310ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@next%2fswc-win32-x64-msvc 285ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@next%2fswc-linux-x64-musl 388ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@next%2fswc-linux-x64-gnu 427ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@next%2fswc-darwin-x64 479ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@next%2fswc-linux-arm64-gnu 405ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@next%2fswc-win32-ia32-msvc 379ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/loose-envify 273ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/loose-envify 245ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/scheduler 344ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@next%2fswc-linux-arm64-musl 515ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@next%2fswc-win32-arm64-msvc 511ms (cache revalidated)
npm http cache https://registry.npmjs.org/eslint 10ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/jiti 152ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/import-fresh 150ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@humanwhocodes%2fobject-schema 161ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/json-schema-traverse 228ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/uri-js 243ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/strip-json-comments 252ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/fastq 253ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@nodelib%2ffs.scandir 257ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/fast-json-stable-stringify 146ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/ansi-styles 142ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/which 217ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/path-key 218ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/supports-color 241ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/shebang-command 225ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/esrecurse 240ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/ms 288ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/estraverse 248ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/estraverse 287ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/flat-cache 289ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/acorn 327ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/acorn-jsx 331ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/path-exists 252ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/locate-path 267ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/type-fest 249ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/argparse 281ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/type-check 259ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/is-extglob 300ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/prelude-ls 269ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/brace-expansion 267ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/prelude-ls 322ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/deep-is 316ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/ansi-regex 208ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/word-wrap 219ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/fast-levenshtein 213ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/resolve-from 264ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/parent-module 274ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/reusify 234ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/run-parallel 241ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@nodelib%2ffs.stat 251ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/queue-microtask 174ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/punycode 158ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/has-flag 173ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/color-convert 226ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/color-name 206ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/isexe 169ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/shebang-regex 178ms (cache revalidated)
npm http cache https://registry.npmjs.org/eslint 7ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/eslint-plugin-import-x 227ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@typescript-eslint%2futils 244ms (cache revalidated)
npm http cache https://registry.npmjs.org/typescript 43ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/glob 199ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/resolve 208ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/is-core-module 230ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@typescript-eslint%2fscope-manager 272ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@typescript-eslint%2fvisitor-keys 354ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@typescript-eslint%2ftypes 433ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@typescript-eslint%2ftypescript-estree 560ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/unrs-resolver 366ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/get-tsconfig 552ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/tinyglobby 581ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/is-bun-module 462ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/stable-hash 577ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@nolyfill%2fis-core-module 285ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@rtsao%2fscc 196ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/array.prototype.flatmap 220ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/array-includes 241ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/eslint-module-utils 227ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/array.prototype.findlastindex 237ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/hasown 226ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/array.prototype.flat 242ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/object.fromentries 213ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/semver 204ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/object.groupby 224ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/object.values 222ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/aria-query 221ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/string.prototype.trimend 233ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/tsconfig-paths 242ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/ast-types-flow 233ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/axe-core 317ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/axobject-query 365ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/damerau-levenshtein 364ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/jsx-ast-utils 350ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/emoji-regex 358ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/safe-regex-test 339ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/language-tags 350ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/string.prototype.includes 195ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/string.prototype.matchall 243ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/array.prototype.findlast 269ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/es-iterator-helpers 305ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/prop-types 309ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/array.prototype.tosorted 324ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/object.entries 316ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/string.prototype.repeat 205ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/once 199ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/inherits 198ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/fs.realpath 200ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/inflight 210ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/path-is-absolute 255ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/globby 183ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/ts-api-utils 192ms (cache revalidated)
npm http cache https://registry.npmjs.org/typescript 72ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/slash 143ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/dir-glob 227ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/fast-glob 234ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/merge2 242ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/array-union 242ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/path-parse 198ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/supports-preserve-symlinks-flag 199ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/call-bound 214ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/define-properties 228ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/es-object-atoms 233ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/is-string 236ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/call-bind 258ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/es-abstract 262ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/get-intrinsic 273ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/call-bind 217ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/math-intrinsics 235ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/es-shim-unscopables 253ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/es-abstract 280ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/es-errors 289ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/es-shim-unscopables 272ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/es-shim-unscopables 268ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/function-bind 210ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/json5 208ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/minimist 164ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/strip-bom 158ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@types%2fjson5 163ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/call-bind-apply-helpers 171ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/has-property-descriptors 168ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/define-data-property 172ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/es-define-property 177ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/object-keys 174ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/call-bind-apply-helpers 181ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/set-function-length 183ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/array-buffer-byte-length 226ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/arraybuffer.prototype.slice 227ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/data-view-byte-length 221ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/available-typed-arrays 230ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/data-view-buffer 271ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/data-view-byte-offset 272ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/es-set-tostringtag 271ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/function.prototype.name 169ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/es-to-primitive 177ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/get-symbol-description 176ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/get-proto 183ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/globalthis 161ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/gopd 162ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/has-proto 166ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/internal-slot 159ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/is-array-buffer 156ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/has-symbols 176ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/is-callable 165ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/is-negative-zero 241ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/is-data-view 255ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/is-regex 254ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/is-set 155ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/is-shared-array-buffer 181ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/is-weakref 173ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/is-typed-array 200ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/object-inspect 160ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/object.assign 162ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/own-keys 171ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/regexp.prototype.flags 180ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/safe-array-concat 158ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/safe-push-apply 157ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/set-proto 166ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/string.prototype.trim 190ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/stop-iteration-iterator 202ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/string.prototype.trimstart 188ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/typed-array-buffer 175ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/typed-array-byte-length 172ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/typed-array-byte-offset 171ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/typed-array-length 156ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/unbox-primitive 199ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/which-typed-array 199ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/has-tostringtag 185ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/dunder-proto 170ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/possible-typed-array-names 175ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/is-symbol 176ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/dunder-proto 176ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/functions-have-names 179ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/is-date-object 181ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/side-channel 184ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/set-function-name 201ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/isarray 210ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/for-each 213ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/for-each 218ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/reflect.getprototypeof 218ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/isarray 226ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/for-each 218ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/reflect.getprototypeof 187ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/has-bigints 178ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/which-boxed-primitive 177ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/language-subtag-registry 178ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/iterator.prototype 163ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/object-assign 162ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/react-is 187ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/keyv 170ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/rimraf 175ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/flatted 180ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/p-locate 196ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/json-buffer 172ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/wrappy 164ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/wrappy 167ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/micromatch 174ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/path-type 181ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/braces 157ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/picomatch 159ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/callsites 155ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/side-channel-map 150ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/side-channel-weakmap 153ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/side-channel-list 156ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/p-limit 172ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/fill-range 169ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/to-regex-range 144ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/is-number 165ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/balanced-match 160ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/concat-map 163ms (cache revalidated)
npm http cache https://registry.npmjs.org/react 43ms (cache hit)
npm http fetch GET 200 https://registry.npmjs.org/nanoid 167ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/tslib 178ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/picocolors 179ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/source-map-js 180ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/client-only 182ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/glob-to-regexp 183ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/streamsearch 185ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/yocto-queue 157ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/js-tokens 167ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/which-builtin-type 164ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/is-finalizationregistry 160ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/is-async-function 176ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/is-generator-function 176ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/which-collection 178ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/is-boolean-object 155ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/is-number-object 157ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/is-bigint 160ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/is-weakset 180ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/is-map 185ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/async-function 187ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/is-weakmap 188ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/fdir 176ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/napi-postinstall 184ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-win32-arm64-msvc 187ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/resolve-pkg-maps 200ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-win32-ia32-msvc 213ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-win32-x64-msvc 221ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-linux-x64-gnu 225ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-linux-x64-musl 250ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-linux-arm64-musl 222ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-linux-arm64-gnu 245ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-android-arm-eabi 224ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-freebsd-x64 270ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-android-arm64 270ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-linux-arm-gnueabihf 236ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-linux-arm-musleabihf 205ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-linux-ppc64-gnu 204ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-darwin-x64 192ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-linux-s390x-gnu 198ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-linux-riscv64-gnu 213ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-linux-riscv64-musl 214ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-darwin-arm64 200ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-wasm32-wasi 218ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@napi-rs%2fwasm-runtime 179ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@emnapi%2fcore 160ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@emnapi%2fruntime 163ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@tybys%2fwasm-util 167ms (cache revalidated)
npm http fetch GET 200 https://registry.npmjs.org/@emnapi%2fwasi-threads 189ms (cache revalidated)
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@unrs/resolver-binding-win32-x64-msvc
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@unrs/resolver-binding-win32-ia32-msvc
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@unrs/resolver-binding-win32-arm64-msvc
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@unrs/resolver-binding-wasm32-wasi
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@napi-rs/wasm-runtime
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@emnapi/core
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@emnapi/runtime
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@tybys/wasm-util
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@emnapi/wasi-threads
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@unrs/resolver-binding-linux-x64-musl
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@unrs/resolver-binding-linux-s390x-gnu
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@unrs/resolver-binding-linux-riscv64-musl
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@unrs/resolver-binding-linux-riscv64-gnu
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@unrs/resolver-binding-linux-ppc64-gnu
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@unrs/resolver-binding-linux-arm64-musl
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@unrs/resolver-binding-linux-arm64-gnu
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@unrs/resolver-binding-linux-arm-musleabihf
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@unrs/resolver-binding-linux-arm-gnueabihf
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@unrs/resolver-binding-freebsd-x64
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@unrs/resolver-binding-darwin-x64
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@unrs/resolver-binding-darwin-arm64
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@unrs/resolver-binding-android-arm64
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@unrs/resolver-binding-android-arm-eabi
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@next/swc-win32-x64-msvc
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@next/swc-win32-ia32-msvc
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@next/swc-win32-arm64-msvc
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@next/swc-linux-x64-musl
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@next/swc-linux-arm64-musl
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@next/swc-linux-arm64-gnu
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@next/swc-darwin-x64
npm verbose reify failed optional dependency /home/<USER>/mehdi-portfolio-website/node_modules/@next/swc-darwin-arm64
npm http cache @tybys/wasm-util@https://registry.npmjs.org/@tybys/wasm-util/-/wasm-util-0.10.0.tgz 0ms (cache hit)
npm http cache @emnapi/wasi-threads@https://registry.npmjs.org/@emnapi/wasi-threads/-/wasi-threads-1.0.4.tgz 0ms (cache hit)
npm http cache @emnapi/runtime@https://registry.npmjs.org/@emnapi/runtime/-/runtime-1.4.5.tgz 0ms (cache hit)
npm http cache @emnapi/core@https://registry.npmjs.org/@emnapi/core/-/core-1.4.5.tgz 0ms (cache hit)
npm http cache napi-postinstall@https://registry.npmjs.org/napi-postinstall/-/napi-postinstall-0.3.2.tgz 0ms (cache hit)
npm http cache @napi-rs/wasm-runtime@https://registry.npmjs.org/@napi-rs/wasm-runtime/-/wasm-runtime-0.2.12.tgz 0ms (cache hit)
npm http cache unrs-resolver@https://registry.npmjs.org/unrs-resolver/-/unrs-resolver-1.11.1.tgz 0ms (cache hit)
npm http cache resolve-pkg-maps@https://registry.npmjs.org/resolve-pkg-maps/-/resolve-pkg-maps-1.0.0.tgz 0ms (cache hit)
npm http cache tinyglobby@https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.14.tgz 0ms (cache hit)
npm http cache @unrs/resolver-binding-linux-x64-gnu@https://registry.npmjs.org/@unrs/resolver-binding-linux-x64-gnu/-/resolver-binding-linux-x64-gnu-1.11.1.tgz 0ms (cache hit)
npm http cache stable-hash@https://registry.npmjs.org/stable-hash/-/stable-hash-0.0.5.tgz 0ms (cache hit)
npm http cache is-bun-module@https://registry.npmjs.org/is-bun-module/-/is-bun-module-2.0.0.tgz 0ms (cache hit)
npm http cache get-tsconfig@https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.10.1.tgz 0ms (cache hit)
npm http cache @nolyfill/is-core-module@https://registry.npmjs.org/@nolyfill/is-core-module/-/is-core-module-1.0.39.tgz 0ms (cache hit)
npm http cache is-weakset@https://registry.npmjs.org/is-weakset/-/is-weakset-2.0.4.tgz 0ms (cache hit)
npm http cache is-weakmap@https://registry.npmjs.org/is-weakmap/-/is-weakmap-2.0.2.tgz 0ms (cache hit)
npm http cache async-function@https://registry.npmjs.org/async-function/-/async-function-1.0.0.tgz 0ms (cache hit)
npm http cache is-map@https://registry.npmjs.org/is-map/-/is-map-2.0.3.tgz 0ms (cache hit)
npm http cache is-generator-function@https://registry.npmjs.org/is-generator-function/-/is-generator-function-1.1.0.tgz 0ms (cache hit)
npm http cache which-collection@https://registry.npmjs.org/which-collection/-/which-collection-1.0.2.tgz 0ms (cache hit)
npm http cache is-finalizationregistry@https://registry.npmjs.org/is-finalizationregistry/-/is-finalizationregistry-1.1.1.tgz 0ms (cache hit)
npm http cache is-async-function@https://registry.npmjs.org/is-async-function/-/is-async-function-2.1.1.tgz 0ms (cache hit)
npm http cache is-number-object@https://registry.npmjs.org/is-number-object/-/is-number-object-1.1.1.tgz 0ms (cache hit)
npm http cache is-boolean-object@https://registry.npmjs.org/is-boolean-object/-/is-boolean-object-1.2.2.tgz 0ms (cache hit)
npm http cache is-bigint@https://registry.npmjs.org/is-bigint/-/is-bigint-1.1.0.tgz 0ms (cache hit)
npm http cache isexe@https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz 0ms (cache hit)
npm http cache glob-to-regexp@https://registry.npmjs.org/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz 0ms (cache hit)
npm http cache punycode@https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz 0ms (cache hit)
npm http cache has-bigints@https://registry.npmjs.org/has-bigints/-/has-bigints-1.1.0.tgz 0ms (cache hit)
npm http cache which-boxed-primitive@https://registry.npmjs.org/which-boxed-primitive/-/which-boxed-primitive-1.1.1.tgz 0ms (cache hit)
npm http cache which-builtin-type@https://registry.npmjs.org/which-builtin-type/-/which-builtin-type-1.2.1.tgz 0ms (cache hit)
npm http cache reflect.getprototypeof@https://registry.npmjs.org/reflect.getprototypeof/-/reflect.getprototypeof-1.0.10.tgz 0ms (cache hit)
npm http cache strip-bom@https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz 0ms (cache hit)
npm http cache for-each@https://registry.npmjs.org/for-each/-/for-each-0.3.5.tgz 0ms (cache hit)
npm http cache minimist@https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz 0ms (cache hit)
npm http cache json5@https://registry.npmjs.org/json5/-/json5-1.0.2.tgz 0ms (cache hit)
npm http cache is-number@https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz 0ms (cache hit)
npm http cache @types/json5@https://registry.npmjs.org/@types/json5/-/json5-0.0.29.tgz 0ms (cache hit)
npm http cache client-only@https://registry.npmjs.org/client-only/-/client-only-0.0.1.tgz 0ms (cache hit)
npm http cache has-flag@https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz 0ms (cache hit)
npm http cache ansi-regex@https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz 0ms (cache hit)
npm http cache side-channel-weakmap@https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz 0ms (cache hit)
npm http cache side-channel-list@https://registry.npmjs.org/side-channel-list/-/side-channel-list-1.0.0.tgz 0ms (cache hit)
npm http cache side-channel-map@https://registry.npmjs.org/side-channel-map/-/side-channel-map-1.0.1.tgz 0ms (cache hit)
npm http cache shebang-regex@https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz 0ms (cache hit)
npm http cache queue-microtask@https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz 0ms (cache hit)
npm http cache isarray@https://registry.npmjs.org/isarray/-/isarray-2.0.5.tgz 0ms (cache hit)
npm http cache supports-preserve-symlinks-flag@https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz 0ms (cache hit)
npm http cache path-parse@https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz 0ms (cache hit)
npm http cache scheduler@https://registry.npmjs.org/scheduler/-/scheduler-0.23.2.tgz 0ms (cache hit)
npm http cache js-tokens@https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz 0ms (cache hit)
npm http cache react-is@https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz 0ms (cache hit)
npm http cache object-assign@https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz 0ms (cache hit)
npm http cache loose-envify@https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz 0ms (cache hit)
npm http cache source-map-js@https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz 0ms (cache hit)
npm http cache nanoid@https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz 0ms (cache hit)
npm http cache picocolors@https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz 0ms (cache hit)
npm http cache callsites@https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz 0ms (cache hit)
npm http cache yocto-queue@https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz 1ms (cache hit)
npm http cache p-limit@https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz 0ms (cache hit)
npm http cache fast-levenshtein@https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz 0ms (cache hit)
npm http cache word-wrap@https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.5.tgz 0ms (cache hit)
npm http cache deep-is@https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz 0ms (cache hit)
npm http cache tslib@https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz 0ms (cache hit)
npm http cache streamsearch@https://registry.npmjs.org/streamsearch/-/streamsearch-1.1.0.tgz 0ms (cache hit)
npm http cache watchpack@https://registry.npmjs.org/watchpack/-/watchpack-2.4.0.tgz 0ms (cache hit)
npm http cache styled-jsx@https://registry.npmjs.org/styled-jsx/-/styled-jsx-5.1.1.tgz 0ms (cache hit)
npm http cache postcss@https://registry.npmjs.org/postcss/-/postcss-8.4.31.tgz 0ms (cache hit)
npm http cache graceful-fs@https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz 0ms (cache hit)
npm http cache caniuse-lite@https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001731.tgz 0ms (cache hit)
npm http cache busboy@https://registry.npmjs.org/busboy/-/busboy-1.6.0.tgz 0ms (cache hit)
npm http cache @swc/helpers@https://registry.npmjs.org/@swc/helpers/-/helpers-0.5.2.tgz 0ms (cache hit)
npm http cache @next/swc-linux-x64-gnu@https://registry.npmjs.org/@next/swc-linux-x64-gnu/-/swc-linux-x64-gnu-14.0.4.tgz 0ms (cache hit)
npm http cache @next/env@https://registry.npmjs.org/@next/env/-/env-14.0.4.tgz 0ms (cache hit)
npm http cache concat-map@https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz 0ms (cache hit)
npm http cache balanced-match@https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz 0ms (cache hit)
npm http cache brace-expansion@https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz 0ms (cache hit)
npm http cache to-regex-range@https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz 0ms (cache hit)
npm http cache fill-range@https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz 0ms (cache hit)
npm http cache picomatch@https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz 0ms (cache hit)
npm http cache p-locate@https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz 0ms (cache hit)
npm http cache braces@https://registry.npmjs.org/braces/-/braces-3.0.3.tgz 0ms (cache hit)
npm http cache type-check@https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz 0ms (cache hit)
npm http cache prelude-ls@https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz 0ms (cache hit)
npm http cache json-buffer@https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.1.tgz 0ms (cache hit)
npm http cache language-subtag-registry@https://registry.npmjs.org/language-subtag-registry/-/language-subtag-registry-0.3.23.tgz 1ms (cache hit)
npm http cache argparse@https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz 0ms (cache hit)
npm http cache set-function-name@https://registry.npmjs.org/set-function-name/-/set-function-name-2.0.2.tgz 0ms (cache hit)
npm http cache side-channel@https://registry.npmjs.org/side-channel/-/side-channel-1.1.0.tgz 0ms (cache hit)
npm http cache is-extglob@https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz 0ms (cache hit)
npm http cache wrappy@https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz 0ms (cache hit)
npm http cache resolve-from@https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz 0ms (cache hit)
npm http cache parent-module@https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz 0ms (cache hit)
npm http cache micromatch@https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz 0ms (cache hit)
npm http cache path-type@https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz 0ms (cache hit)
npm http cache merge2@https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz 0ms (cache hit)
npm http cache slash@https://registry.npmjs.org/slash/-/slash-3.0.0.tgz 0ms (cache hit)
npm http cache fast-glob@https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.3.tgz 0ms (cache hit)
npm http cache dir-glob@https://registry.npmjs.org/dir-glob/-/dir-glob-3.0.1.tgz 0ms (cache hit)
npm http cache type-fest@https://registry.npmjs.org/type-fest/-/type-fest-0.20.2.tgz 0ms (cache hit)
npm http cache array-union@https://registry.npmjs.org/array-union/-/array-union-2.1.0.tgz 0ms (cache hit)
npm http cache path-is-absolute@https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz 0ms (cache hit)
npm http cache once@https://registry.npmjs.org/once/-/once-1.4.0.tgz 0ms (cache hit)
npm http cache inherits@https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz 0ms (cache hit)
npm http cache inflight@https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz 1ms (cache hit)
npm http cache fs.realpath@https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz 0ms (cache hit)
npm http cache dunder-proto@https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz 0ms (cache hit)
npm http cache functions-have-names@https://registry.npmjs.org/functions-have-names/-/functions-have-names-1.2.3.tgz 0ms (cache hit)
npm http cache rimraf@https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz 0ms (cache hit)
npm http cache path-exists@https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz 0ms (cache hit)
npm http cache flatted@https://registry.npmjs.org/flatted/-/flatted-3.3.3.tgz 0ms (cache hit)
npm http cache locate-path@https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz 0ms (cache hit)
npm http cache reusify@https://registry.npmjs.org/reusify/-/reusify-1.1.0.tgz 0ms (cache hit)
npm http cache flat-cache@https://registry.npmjs.org/flat-cache/-/flat-cache-3.2.0.tgz 0ms (cache hit)
npm http cache acorn-jsx@https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz 0ms (cache hit)
npm http cache acorn@https://registry.npmjs.org/acorn/-/acorn-8.15.0.tgz 0ms (cache hit)
npm http cache keyv@https://registry.npmjs.org/keyv/-/keyv-4.5.4.tgz 0ms (cache hit)
npm http cache esrecurse@https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz 0ms (cache hit)
npm http cache iterator.prototype@https://registry.npmjs.org/iterator.prototype/-/iterator.prototype-1.1.5.tgz 0ms (cache hit)
npm http cache string.prototype.repeat@https://registry.npmjs.org/string.prototype.repeat/-/string.prototype.repeat-1.0.0.tgz 0ms (cache hit)
npm http cache string.prototype.matchall@https://registry.npmjs.org/string.prototype.matchall/-/string.prototype.matchall-4.0.12.tgz 0ms (cache hit)
npm http cache prop-types@https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz 0ms (cache hit)
npm http cache object.entries@https://registry.npmjs.org/object.entries/-/object.entries-1.1.9.tgz 0ms (cache hit)
npm http cache estraverse@https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz 0ms (cache hit)
npm http cache es-iterator-helpers@https://registry.npmjs.org/es-iterator-helpers/-/es-iterator-helpers-1.2.1.tgz 0ms (cache hit)
npm http cache array.prototype.tosorted@https://registry.npmjs.org/array.prototype.tosorted/-/array.prototype.tosorted-1.1.4.tgz 0ms (cache hit)
npm http cache array.prototype.findlast@https://registry.npmjs.org/array.prototype.findlast/-/array.prototype.findlast-1.2.5.tgz 0ms (cache hit)
npm http cache string.prototype.includes@https://registry.npmjs.org/string.prototype.includes/-/string.prototype.includes-2.0.1.tgz 0ms (cache hit)
npm http cache language-tags@https://registry.npmjs.org/language-tags/-/language-tags-1.0.9.tgz 0ms (cache hit)
npm http cache jsx-ast-utils@https://registry.npmjs.org/jsx-ast-utils/-/jsx-ast-utils-3.3.5.tgz 0ms (cache hit)
npm http cache emoji-regex@https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz 0ms (cache hit)
npm http cache damerau-levenshtein@https://registry.npmjs.org/damerau-levenshtein/-/damerau-levenshtein-1.0.8.tgz 0ms (cache hit)
npm http cache axobject-query@https://registry.npmjs.org/axobject-query/-/axobject-query-4.1.0.tgz 0ms (cache hit)
npm http cache axe-core@https://registry.npmjs.org/axe-core/-/axe-core-4.10.3.tgz 0ms (cache hit)
npm http cache ast-types-flow@https://registry.npmjs.org/ast-types-flow/-/ast-types-flow-0.0.8.tgz 0ms (cache hit)
npm http cache is-symbol@https://registry.npmjs.org/is-symbol/-/is-symbol-1.1.1.tgz 0ms (cache hit)
npm http cache aria-query@https://registry.npmjs.org/aria-query/-/aria-query-5.3.2.tgz 0ms (cache hit)
npm http cache has-tostringtag@https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz 0ms (cache hit)
npm http cache which-typed-array@https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.19.tgz 0ms (cache hit)
npm http cache possible-typed-array-names@https://registry.npmjs.org/possible-typed-array-names/-/possible-typed-array-names-1.1.0.tgz 0ms (cache hit)
npm http cache unbox-primitive@https://registry.npmjs.org/unbox-primitive/-/unbox-primitive-1.1.0.tgz 0ms (cache hit)
npm http cache typed-array-length@https://registry.npmjs.org/typed-array-length/-/typed-array-length-1.0.7.tgz 0ms (cache hit)
npm http cache typed-array-byte-offset@https://registry.npmjs.org/typed-array-byte-offset/-/typed-array-byte-offset-1.0.4.tgz 0ms (cache hit)
npm http cache typed-array-buffer@https://registry.npmjs.org/typed-array-buffer/-/typed-array-buffer-1.0.3.tgz 0ms (cache hit)
npm http cache typed-array-byte-length@https://registry.npmjs.org/typed-array-byte-length/-/typed-array-byte-length-1.0.3.tgz 0ms (cache hit)
npm http cache is-date-object@https://registry.npmjs.org/is-date-object/-/is-date-object-1.1.0.tgz 0ms (cache hit)
npm http cache string.prototype.trimstart@https://registry.npmjs.org/string.prototype.trimstart/-/string.prototype.trimstart-1.0.8.tgz 0ms (cache hit)
npm http cache string.prototype.trim@https://registry.npmjs.org/string.prototype.trim/-/string.prototype.trim-1.2.10.tgz 0ms (cache hit)
npm http cache stop-iteration-iterator@https://registry.npmjs.org/stop-iteration-iterator/-/stop-iteration-iterator-1.1.0.tgz 0ms (cache hit)
npm http cache set-proto@https://registry.npmjs.org/set-proto/-/set-proto-1.0.0.tgz 0ms (cache hit)
npm http cache safe-push-apply@https://registry.npmjs.org/safe-push-apply/-/safe-push-apply-1.0.0.tgz 0ms (cache hit)
npm http cache safe-regex-test@https://registry.npmjs.org/safe-regex-test/-/safe-regex-test-1.1.0.tgz 0ms (cache hit)
npm http cache safe-array-concat@https://registry.npmjs.org/safe-array-concat/-/safe-array-concat-1.1.3.tgz 0ms (cache hit)
npm http cache regexp.prototype.flags@https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.5.4.tgz 0ms (cache hit)
npm http cache own-keys@https://registry.npmjs.org/own-keys/-/own-keys-1.0.1.tgz 0ms (cache hit)
npm http cache object.assign@https://registry.npmjs.org/object.assign/-/object.assign-4.1.7.tgz 0ms (cache hit)
npm http cache object-inspect@https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.4.tgz 0ms (cache hit)
npm http cache is-weakref@https://registry.npmjs.org/is-weakref/-/is-weakref-1.1.1.tgz 0ms (cache hit)
npm http cache is-typed-array@https://registry.npmjs.org/is-typed-array/-/is-typed-array-1.1.15.tgz 0ms (cache hit)
npm http cache is-shared-array-buffer@https://registry.npmjs.org/is-shared-array-buffer/-/is-shared-array-buffer-1.0.4.tgz 0ms (cache hit)
npm http cache is-regex@https://registry.npmjs.org/is-regex/-/is-regex-1.2.1.tgz 0ms (cache hit)
npm http cache is-set@https://registry.npmjs.org/is-set/-/is-set-2.0.3.tgz 0ms (cache hit)
npm http cache is-negative-zero@https://registry.npmjs.org/is-negative-zero/-/is-negative-zero-2.0.3.tgz 1ms (cache hit)
npm http cache is-data-view@https://registry.npmjs.org/is-data-view/-/is-data-view-1.0.2.tgz 0ms (cache hit)
npm http cache is-callable@https://registry.npmjs.org/is-callable/-/is-callable-1.2.7.tgz 0ms (cache hit)
npm http cache is-array-buffer@https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-3.0.5.tgz 0ms (cache hit)
npm http cache internal-slot@https://registry.npmjs.org/internal-slot/-/internal-slot-1.1.0.tgz 0ms (cache hit)
npm http cache has-symbols@https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz 0ms (cache hit)
npm http cache globalthis@https://registry.npmjs.org/globalthis/-/globalthis-1.0.4.tgz 0ms (cache hit)
npm http cache has-proto@https://registry.npmjs.org/has-proto/-/has-proto-1.2.0.tgz 0ms (cache hit)
npm http cache get-symbol-description@https://registry.npmjs.org/get-symbol-description/-/get-symbol-description-1.1.0.tgz 0ms (cache hit)
npm http cache get-proto@https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz 0ms (cache hit)
npm http cache function.prototype.name@https://registry.npmjs.org/function.prototype.name/-/function.prototype.name-1.1.8.tgz 0ms (cache hit)
npm http cache es-to-primitive@https://registry.npmjs.org/es-to-primitive/-/es-to-primitive-1.3.0.tgz 0ms (cache hit)
npm http cache es-set-tostringtag@https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz 0ms (cache hit)
npm http cache data-view-byte-offset@https://registry.npmjs.org/data-view-byte-offset/-/data-view-byte-offset-1.0.1.tgz 0ms (cache hit)
npm http cache data-view-byte-length@https://registry.npmjs.org/data-view-byte-length/-/data-view-byte-length-1.0.2.tgz 0ms (cache hit)
npm http cache available-typed-arrays@https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz 0ms (cache hit)
npm http cache array-buffer-byte-length@https://registry.npmjs.org/array-buffer-byte-length/-/array-buffer-byte-length-1.0.2.tgz 0ms (cache hit)
npm http cache data-view-buffer@https://registry.npmjs.org/data-view-buffer/-/data-view-buffer-1.0.2.tgz 0ms (cache hit)
npm http cache arraybuffer.prototype.slice@https://registry.npmjs.org/arraybuffer.prototype.slice/-/arraybuffer.prototype.slice-1.0.4.tgz 0ms (cache hit)
npm http cache gopd@https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz 0ms (cache hit)
npm http cache object-keys@https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz 0ms (cache hit)
npm http cache define-data-property@https://registry.npmjs.org/define-data-property/-/define-data-property-1.1.4.tgz 0ms (cache hit)
npm http cache function-bind@https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz 0ms (cache hit)
npm http cache has-property-descriptors@https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz 0ms (cache hit)
npm http cache set-function-length@https://registry.npmjs.org/set-function-length/-/set-function-length-1.2.2.tgz 0ms (cache hit)
npm http cache es-define-property@https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz 0ms (cache hit)
npm http cache call-bind-apply-helpers@https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz 0ms (cache hit)
npm http cache es-shim-unscopables@https://registry.npmjs.org/es-shim-unscopables/-/es-shim-unscopables-1.1.0.tgz 0ms (cache hit)
npm http cache es-errors@https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz 0ms (cache hit)
npm http cache math-intrinsics@https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz 0ms (cache hit)
npm http cache is-string@https://registry.npmjs.org/is-string/-/is-string-1.1.1.tgz 0ms (cache hit)
npm http cache get-intrinsic@https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz 0ms (cache hit)
npm http cache es-object-atoms@https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz 0ms (cache hit)
npm http cache define-properties@https://registry.npmjs.org/define-properties/-/define-properties-1.2.1.tgz 0ms (cache hit)
npm http cache es-abstract@https://registry.npmjs.org/es-abstract/-/es-abstract-1.24.0.tgz 0ms (cache hit)
npm http cache call-bound@https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz 0ms (cache hit)
npm http cache call-bind@https://registry.npmjs.org/call-bind/-/call-bind-1.0.8.tgz 0ms (cache hit)
npm http cache tsconfig-paths@https://registry.npmjs.org/tsconfig-paths/-/tsconfig-paths-3.15.0.tgz 0ms (cache hit)
npm http cache string.prototype.trimend@https://registry.npmjs.org/string.prototype.trimend/-/string.prototype.trimend-1.0.9.tgz 0ms (cache hit)
npm http cache object.values@https://registry.npmjs.org/object.values/-/object.values-1.2.1.tgz 0ms (cache hit)
npm http cache object.groupby@https://registry.npmjs.org/object.groupby/-/object.groupby-1.0.3.tgz 0ms (cache hit)
npm http cache object.fromentries@https://registry.npmjs.org/object.fromentries/-/object.fromentries-2.0.8.tgz 0ms (cache hit)
npm http cache eslint-module-utils@https://registry.npmjs.org/eslint-module-utils/-/eslint-module-utils-2.12.1.tgz 0ms (cache hit)
npm http cache array.prototype.flatmap@https://registry.npmjs.org/array.prototype.flatmap/-/array.prototype.flatmap-1.3.3.tgz 0ms (cache hit)
npm http cache hasown@https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz 0ms (cache hit)
npm http cache array.prototype.findlastindex@https://registry.npmjs.org/array.prototype.findlastindex/-/array.prototype.findlastindex-1.2.6.tgz 0ms (cache hit)
npm http cache array.prototype.flat@https://registry.npmjs.org/array.prototype.flat/-/array.prototype.flat-1.3.3.tgz 0ms (cache hit)
npm http cache array-includes@https://registry.npmjs.org/array-includes/-/array-includes-3.1.9.tgz 0ms (cache hit)
npm http cache @rtsao/scc@https://registry.npmjs.org/@rtsao/scc/-/scc-1.1.0.tgz 0ms (cache hit)
npm http cache resolve@https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz 0ms (cache hit)
npm http cache is-core-module@https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz 0ms (cache hit)
npm http cache ts-api-utils@https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-1.4.3.tgz 0ms (cache hit)
npm http cache semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 0ms (cache hit)
npm http cache globby@https://registry.npmjs.org/globby/-/globby-11.1.0.tgz 0ms (cache hit)
npm http cache @typescript-eslint/visitor-keys@https://registry.npmjs.org/@typescript-eslint/visitor-keys/-/visitor-keys-6.21.0.tgz 0ms (cache hit)
npm http cache @typescript-eslint/typescript-estree@https://registry.npmjs.org/@typescript-eslint/typescript-estree/-/typescript-estree-6.21.0.tgz 0ms (cache hit)
npm http cache @typescript-eslint/types@https://registry.npmjs.org/@typescript-eslint/types/-/types-6.21.0.tgz 0ms (cache hit)
npm http cache @typescript-eslint/scope-manager@https://registry.npmjs.org/@typescript-eslint/scope-manager/-/scope-manager-6.21.0.tgz 0ms (cache hit)
npm http cache glob@https://registry.npmjs.org/glob/-/glob-7.1.7.tgz 0ms (cache hit)
npm http cache eslint-plugin-react@https://registry.npmjs.org/eslint-plugin-react/-/eslint-plugin-react-7.37.5.tgz 0ms (cache hit)
npm http cache eslint-plugin-react-hooks@https://registry.npmjs.org/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-5.0.0-canary-7118f5dd7-20230705.tgz 0ms (cache hit)
npm http cache eslint-plugin-import@https://registry.npmjs.org/eslint-plugin-import/-/eslint-plugin-import-2.32.0.tgz 0ms (cache hit)
npm http cache eslint-plugin-jsx-a11y@https://registry.npmjs.org/eslint-plugin-jsx-a11y/-/eslint-plugin-jsx-a11y-6.10.2.tgz 0ms (cache hit)
npm http cache eslint-import-resolver-node@https://registry.npmjs.org/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.9.tgz 0ms (cache hit)
npm http cache @typescript-eslint/parser@https://registry.npmjs.org/@typescript-eslint/parser/-/parser-6.21.0.tgz 0ms (cache hit)
npm http cache @rushstack/eslint-patch@https://registry.npmjs.org/@rushstack/eslint-patch/-/eslint-patch-1.12.0.tgz 0ms (cache hit)
npm http cache @next/eslint-plugin-next@https://registry.npmjs.org/@next/eslint-plugin-next/-/eslint-plugin-next-14.0.4.tgz 0ms (cache hit)
npm http cache ms@https://registry.npmjs.org/ms/-/ms-2.1.3.tgz 0ms (cache hit)
npm http cache which@https://registry.npmjs.org/which/-/which-2.0.2.tgz 0ms (cache hit)
npm http cache shebang-command@https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz 0ms (cache hit)
npm http cache path-key@https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz 0ms (cache hit)
npm http cache color-name@https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz 0ms (cache hit)
npm http cache color-convert@https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz 0ms (cache hit)
npm http cache supports-color@https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz 0ms (cache hit)
npm http cache ansi-styles@https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz 0ms (cache hit)
npm http cache uri-js@https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz 0ms (cache hit)
npm http cache json-schema-traverse@https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz 0ms (cache hit)
npm http cache fast-json-stable-stringify@https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz 0ms (cache hit)
npm http cache run-parallel@https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz 0ms (cache hit)
npm http cache @nodelib/fs.stat@https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz 0ms (cache hit)
npm http cache fastq@https://registry.npmjs.org/fastq/-/fastq-1.19.1.tgz 0ms (cache hit)
npm http cache @nodelib/fs.scandir@https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz 0ms (cache hit)
npm http cache strip-json-comments@https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz 0ms (cache hit)
npm http cache @humanwhocodes/object-schema@https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-2.0.3.tgz 0ms (cache hit)
npm http cache import-fresh@https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.1.tgz 0ms (cache hit)
npm http cache text-table@https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz 0ms (cache hit)
npm http cache strip-ansi@https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz 0ms (cache hit)
npm http cache optionator@https://registry.npmjs.org/optionator/-/optionator-0.9.4.tgz 0ms (cache hit)
npm http cache natural-compare@https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz 0ms (cache hit)
npm http cache minimatch@https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz 0ms (cache hit)
npm http cache lodash.merge@https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz 0ms (cache hit)
npm http cache json-stable-stringify-without-jsonify@https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz 0ms (cache hit)
npm http cache levn@https://registry.npmjs.org/levn/-/levn-0.4.1.tgz 0ms (cache hit)
npm http cache is-path-inside@https://registry.npmjs.org/is-path-inside/-/is-path-inside-3.0.3.tgz 0ms (cache hit)
npm http cache js-yaml@https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz 0ms (cache hit)
npm http cache is-glob@https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz 0ms (cache hit)
npm http cache imurmurhash@https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz 0ms (cache hit)
npm http cache ignore@https://registry.npmjs.org/ignore/-/ignore-5.3.2.tgz 0ms (cache hit)
npm http cache graphemer@https://registry.npmjs.org/graphemer/-/graphemer-1.4.0.tgz 0ms (cache hit)
npm http cache globals@https://registry.npmjs.org/globals/-/globals-13.24.0.tgz 0ms (cache hit)
npm http cache glob-parent@https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz 0ms (cache hit)
npm http cache find-up@https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz 0ms (cache hit)
npm http cache fast-deep-equal@https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz 0ms (cache hit)
npm http cache file-entry-cache@https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-6.0.1.tgz 0ms (cache hit)
npm http cache esutils@https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz 0ms (cache hit)
npm http cache esquery@https://registry.npmjs.org/esquery/-/esquery-1.6.0.tgz 0ms (cache hit)
npm http cache espree@https://registry.npmjs.org/espree/-/espree-9.6.1.tgz 0ms (cache hit)
npm http cache eslint-visitor-keys@https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz 0ms (cache hit)
npm http cache eslint-scope@https://registry.npmjs.org/eslint-scope/-/eslint-scope-7.2.2.tgz 0ms (cache hit)
npm http cache escape-string-regexp@https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz 0ms (cache hit)
npm http cache doctrine@https://registry.npmjs.org/doctrine/-/doctrine-3.0.0.tgz 0ms (cache hit)
npm http cache debug@https://registry.npmjs.org/debug/-/debug-4.4.1.tgz 0ms (cache hit)
npm http cache cross-spawn@https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz 0ms (cache hit)
npm http cache chalk@https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz 0ms (cache hit)
npm http cache ajv@https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz 0ms (cache hit)
npm http cache @nodelib/fs.walk@https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz 0ms (cache hit)
npm http cache @humanwhocodes/module-importer@https://registry.npmjs.org/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz 0ms (cache hit)
npm http cache @humanwhocodes/config-array@https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.11.14.tgz 0ms (cache hit)
npm http cache @eslint/js@https://registry.npmjs.org/@eslint/js/-/js-8.51.0.tgz 0ms (cache hit)
npm http cache @eslint-community/regexpp@https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.12.1.tgz 0ms (cache hit)
npm http cache @eslint/eslintrc@https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-2.1.4.tgz 0ms (cache hit)
npm http cache csstype@https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz 0ms (cache hit)
npm http cache @eslint-community/eslint-utils@https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz 0ms (cache hit)
npm http cache @types/scheduler@https://registry.npmjs.org/@types/scheduler/-/scheduler-0.26.0.tgz 0ms (cache hit)
npm http cache @types/prop-types@https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.15.tgz 0ms (cache hit)
npm http cache typescript@https://registry.npmjs.org/typescript/-/typescript-5.8.3.tgz 0ms (cache hit)
npm http cache react-dom@https://registry.npmjs.org/react-dom/-/react-dom-18.2.0.tgz 0ms (cache hit)
npm http cache next@https://registry.npmjs.org/next/-/next-14.0.4.tgz 0ms (cache hit)
npm http cache react@https://registry.npmjs.org/react/-/react-18.2.0.tgz 0ms (cache hit)
npm http cache eslint-config-next@https://registry.npmjs.org/eslint-config-next/-/eslint-config-next-14.0.4.tgz 0ms (cache hit)
npm http cache eslint@https://registry.npmjs.org/eslint/-/eslint-8.51.0.tgz 0ms (cache hit)
npm http cache @types/react-dom@https://registry.npmjs.org/@types/react-dom/-/react-dom-18.2.0.tgz 0ms (cache hit)
npm http cache @types/node@https://registry.npmjs.org/@types/node/-/node-20.8.0.tgz 0ms (cache hit)
npm http cache @types/react@https://registry.npmjs.org/@types/react/-/react-18.2.0.tgz 0ms (cache hit)
npm http cache picomatch@https://registry.npmjs.org/picomatch/-/picomatch-4.0.3.tgz 0ms (cache hit)
npm http cache fdir@https://registry.npmjs.org/fdir/-/fdir-6.4.6.tgz 0ms (cache hit)
npm http cache glob-parent@https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz 0ms (cache hit)
npm http cache debug@https://registry.npmjs.org/debug/-/debug-3.2.7.tgz 0ms (cache hit)
npm http cache brace-expansion@https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.2.tgz 0ms (cache hit)
npm http cache minimatch@https://registry.npmjs.org/minimatch/-/minimatch-9.0.3.tgz 0ms (cache hit)
npm http cache semver@https://registry.npmjs.org/semver/-/semver-6.3.1.tgz 0ms (cache hit)
npm http cache resolve@https://registry.npmjs.org/resolve/-/resolve-2.0.0-next.5.tgz 0ms (cache hit)
npm http cache doctrine@https://registry.npmjs.org/doctrine/-/doctrine-2.1.0.tgz 0ms (cache hit)
npm http cache semver@https://registry.npmjs.org/semver/-/semver-6.3.1.tgz 0ms (cache hit)
npm http cache debug@https://registry.npmjs.org/debug/-/debug-3.2.7.tgz 0ms (cache hit)
npm http cache doctrine@https://registry.npmjs.org/doctrine/-/doctrine-2.1.0.tgz 0ms (cache hit)
npm http cache debug@https://registry.npmjs.org/debug/-/debug-3.2.7.tgz 0ms (cache hit)
npm http cache eslint-import-resolver-typescript@https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-3.10.1.tgz 0ms (cache hit)
npm http fetch POST 200 https://registry.npmjs.org/-/npm/v1/security/advisories/bulk 1195ms
npm warn deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
npm warn deprecated @humanwhocodes/config-array@0.11.14: Use @eslint/config-array instead
npm warn deprecated rimraf@3.0.2: Rimraf versions prior to v4 are no longer supported
npm warn deprecated @humanwhocodes/object-schema@2.0.3: Use @eslint/object-schema instead
npm warn deprecated glob@7.1.7: Glob versions prior to v9 are no longer supported
npm http fetch GET 200 https://registry.npmjs.org/@emnapi/wasi-threads/-/wasi-threads-1.0.4.tgz 1488ms (cache miss)
npm http fetch GET 200 https://registry.npmjs.org/@emnapi/runtime/-/runtime-1.4.5.tgz 1543ms (cache miss)
npm http fetch GET 200 https://registry.npmjs.org/@tybys/wasm-util/-/wasm-util-0.10.0.tgz 1731ms (cache miss)
npm http fetch GET 200 https://registry.npmjs.org/@emnapi/core/-/core-1.4.5.tgz 1763ms (cache miss)
npm warn deprecated eslint@8.51.0: This version is no longer supported. Please see https://eslint.org/version-support for other options.
npm http fetch GET 200 https://registry.npmjs.org/@napi-rs/wasm-runtime/-/wasm-runtime-0.2.12.tgz 3260ms (cache miss)
npm http fetch GET 200 https://registry.npmjs.org/next 14204ms (cache miss)
npm http fetch GET 200 https://registry.npmjs.org/@next/swc-linux-x64-gnu/-/swc-linux-x64-gnu-14.0.4.tgz 33273ms (cache miss)
npm info run unrs-resolver@1.11.1 postinstall node_modules/unrs-resolver napi-postinstall unrs-resolver 1.11.1 check
npm info run unrs-resolver@1.11.1 postinstall { code: 0, signal: null }

added 336 packages, and audited 337 packages in 58s

124 packages are looking for funding
  run `npm fund` for details

1 critical severity vulnerability

To address all issues, run:
  npm audit fix --force

Run `npm audit` for details.
npm verbose cwd /home/<USER>/mehdi-portfolio-website
npm verbose os Linux 6.13.2-arch1-1
npm verbose node v24.0.2
npm verbose npm  v11.3.0
npm verbose exit 0
npm info ok
