@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Moderat fonts */
@font-face {
  font-family: 'Moderat';
  src: url('/fonts/Moderat-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Moderat';
  src: url('/fonts/Moderat-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Moderat';
  src: url('/fonts/Moderat-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Moderat';
  src: url('/fonts/Moderat-Black.woff2') format('woff2');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

/* Prevent horizontal scroll */
html, body {
  overflow-x: hidden;
  max-width: 100vw;
}

/* Base styles */

:root {
  /* Dark theme - primary artistic palette */
  --background: #0f0f0f;
  --foreground: #fafafa;
  --muted: #a1a1a1;
  --muted-light: #d4d4d4;
  --accent: #ffffff;
  --accent-light: #1a1a1a;
  --border: #2a2a2a;
  --border-light: #404040;
  --surface: #171717;
  --surface-elevated: #262626;
  --surface-overlay: #404040;

  /* Artistic accents */
  --artistic-red: #dc2626;
  --artistic-blue: #2563eb;
  --artistic-yellow: #eab308;
  --artistic-green: #16a34a;

  /* Semantic colors */
  --success: #22c55e;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;
}

@media (prefers-color-scheme: light) {
  :root {
    /* Light theme with warm, gallery-like feel */
    --background: #fefefe;
    --foreground: #0a0a0a;
    --muted: #6b7280;
    --muted-light: #9ca3af;
    --accent: #000000;
    --accent-light: #f5f5f5;
    --border: #e5e7eb;
    --border-light: #d1d5db;
    --surface: #f9fafb;
    --surface-elevated: #ffffff;
    --surface-overlay: #f3f4f6;

    /* Adjusted artistic palette for light mode */
    --artistic-red: #b91c1c;
    --artistic-blue: #1d4ed8;
    --artistic-yellow: #ca8a04;
    --artistic-green: #15803d;

    /* Semantic colors for light mode */
    --success: #16a34a;
    --warning: #d97706;
    --error: #dc2626;
    --info: #2563eb;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Moderat', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.618; /* Golden ratio for optimal readability */
  font-size: 16px;
  font-weight: 400;
}

/* Sophisticated Typography System based on modular scale */
.type-display {
  font-size: clamp(3rem, 8vw, 6rem);
  font-weight: 500;
  line-height: 0.9;
  letter-spacing: -0.04em;
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1, "ss01" 1;
}

.type-heading-xl {
  font-size: clamp(2.5rem, 6vw, 4rem);
  font-weight: 500;
  line-height: 1.1;
  letter-spacing: -0.03em;
}

.type-heading-lg {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 500;
  line-height: 1.2;
  letter-spacing: -0.025em;
}

.type-heading-md {
  font-size: clamp(1.5rem, 3vw, 2rem);
  font-weight: 500;
  line-height: 1.3;
  letter-spacing: -0.02em;
}

.type-heading-sm {
  font-size: clamp(1.25rem, 2.5vw, 1.5rem);
  font-weight: 500;
  line-height: 1.4;
  letter-spacing: -0.015em;
}

.type-body-lg {
  font-size: 1.125rem;
  font-weight: 400;
  line-height: 1.7;
  letter-spacing: -0.01em;
}

.type-body {
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.618;
  letter-spacing: -0.005em;
}

.type-body-sm {
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.6;
  letter-spacing: 0;
}

.type-caption {
  font-size: 0.75rem;
  font-weight: 400;
  line-height: 1.5;
  letter-spacing: 0.01em;
  text-transform: uppercase;
}

/* Legacy classes for backward compatibility */
.artistic-heading {
  @apply type-heading-md;
}

.artistic-body {
  @apply type-body;
}

.artistic-accent {
  color: var(--artistic-red);
  transition: color 0.2s ease;
}

/* Sophisticated responsive layout utilities */
.asymmetric-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min(280px, 100%), 1fr));
  gap: clamp(1rem, 4vw, 2rem);
  align-items: start;
}

.responsive-spatial-grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  grid-template-rows: repeat(12, 1fr);
  gap: clamp(0.5rem, 2vw, 1rem);
  height: 100vh;
  width: 100vw;
}

.artistic-border {
  border: 1px solid var(--border);
}

/* Responsive typography scaling */
@media (max-width: 768px) {
  .type-display {
    font-size: clamp(2rem, 12vw, 4rem);
    line-height: 1;
    letter-spacing: -0.03em;
  }

  .type-heading-xl {
    font-size: clamp(1.75rem, 8vw, 2.5rem);
  }

  .type-heading-lg {
    font-size: clamp(1.5rem, 6vw, 2rem);
  }
}

@media (max-width: 480px) {
  body {
    font-size: 14px;
  }

  .type-display {
    font-size: clamp(1.75rem, 15vw, 3rem);
    letter-spacing: -0.02em;
  }
}

/* Smooth transitions for artistic interactions */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

/* Custom cursor only when spatial canvas is active */
.spatial-canvas {
  cursor: none;
}

/* Spatial layout utilities */
.spatial-element {
  position: absolute;
  transform-origin: center;
}

.spatial-element:hover {
  z-index: 10;
}

/* Minimal scrollbar for overlays */
::-webkit-scrollbar {
  width: 2px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--muted);
  border-radius: 1px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--foreground);
}

/* Focus styles for accessibility */
button:focus-visible,
[tabindex]:focus-visible {
  outline: 1px solid var(--artistic-red);
  outline-offset: 2px;
}

/* Artistic hover states */
.artistic-hover:hover {
  transform: translateY(-2px);
  transition: transform 0.2s ease;
}

/* Text selection styling */
::selection {
  background: var(--artistic-red);
  color: var(--background);
}

.hero-background {
  background-image: url('/images/tehran-thumb.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
