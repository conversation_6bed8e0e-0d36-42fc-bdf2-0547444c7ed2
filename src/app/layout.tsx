import type { <PERSON><PERSON><PERSON> } from "next";
import "./globals.css";
import { ThemeProvider } from "next-themes";
import ErrorBoundary from "@/components/ErrorBoundary";

export const metadata: Metadata = {
  title: "<PERSON><PERSON> — Visual Artist & Researcher",
  description: "Tehran-based visual artist and researcher exploring the intersection of design, art, and cultural expression through experimental typography and cross-media artwork.",
  keywords: "visual artist, Tehran, experimental design, typography, performance art, cultural research",
  authors: [{ name: "<PERSON><PERSON>" }],
  creator: "<PERSON><PERSON>",
  openGraph: {
    title: "<PERSON><PERSON> — Visual Artist & Researcher",
    description: "Tehran-based visual artist exploring design, art, and cultural expression",
    type: "website",
    locale: "en_US",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        {/* Remove Inter - we're using Moderat exclusively for design consistency */}
      </head>
      <body className="antialiased font-moderat" suppressHydrationWarning>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <ErrorBoundary>
            {children}
          </ErrorBoundary>
        </ThemeProvider>
      </body>
    </html>
  );
}

