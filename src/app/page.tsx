"use client";
import { useEffect, useState } from "react";
import dynamic from "next/dynamic";
import LoadingScreen from "@/components/LoadingScreen";
import SpatialCanvas from "@/components/SpatialCanvasNew";
import Contact from "@/components/ContactSection";
import Footer from "@/components/Footer";
import Navbar from "@/components/Navbar";
import CustomCursor from "@/components/CustomCursor";

export default function Home() {
  const [mounted, setMounted] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    setMounted(true);
    // Simple loading timer
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  const handleLoadingComplete = () => {
    setIsLoading(false);
  };

  if (!mounted) {
    // This is a simple fallback for the initial mount, you can style it as you wish
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="type-body text-muted animate-pulse">
          Initializing...
        </div>
      </div>
    );
  }

  return (
    <>
      <LoadingScreen isLoading={isLoading} onComplete={handleLoadingComplete} />
      <CustomCursor />
      <div className={`transition-opacity duration-500 ${isLoading ? 'opacity-0' : 'opacity-100'}`}>
        <Navbar />
        <main className="flex flex-col">
          <SpatialCanvas />
          <Contact />
        </main>
        <Footer />
      </div>
    </>
  );
}
