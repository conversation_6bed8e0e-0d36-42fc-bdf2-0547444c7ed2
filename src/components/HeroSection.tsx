"use client";
import { motion } from "framer-motion";

export default function Hero() {
	return (
		<section className="min-h-screen flex items-center px-4 py-20">
			<div className="max-w-7xl mx-auto w-full">
				<div className="text-center">
					<motion.div
						initial={{ opacity: 0, y: 30 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.8, delay: 0.4 }}
						className="space-y-4"
					>
						<h1 className="text-6xl md:text-8xl font-medium tracking-tight">
							<span className="block">Mehdi</span>
							<span className="block text-artistic-red">Asadi</span>
						</h1>
						<p className="text-xl md:text-2xl text-muted leading-relaxed max-w-2xl mx-auto">
							Visual artist & researcher exploring the intersection of
							<span className="artistic-accent"> design, culture,</span> and
							<span className="artistic-accent"> human expression</span> in Tehran.
						</p>
					</motion.div>
				</div>
			</div>
		</section>
	);
}
