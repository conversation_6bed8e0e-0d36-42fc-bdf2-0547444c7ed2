"use client";
import { motion, useScroll, useTransform } from "framer-motion";
import { useRef } from "react";

export default function Hero() {
	const containerRef = useRef<HTMLElement>(null);
	const { scrollYProgress } = useScroll({
		target: containerRef,
		offset: ["start start", "end start"]
	});

	// Persian-inspired geometric transformations
	const nameScale = useTransform(scrollYProgress, [0, 0.5], [1, 0.95]);
	const nameOpacity = useTransform(scrollYProgress, [0, 0.3], [1, 0.8]);
	const letterSpacing = useTransform(scrollYProgress, [0, 0.4], ["0em", "0.05em"]);

	return (
		<section
			ref={containerRef}
			className="relative min-h-screen flex items-center px-4 py-20 overflow-hidden"
		>
			{/* Persian geometric pattern overlay - subtle cultural reference */}
			<div className="absolute inset-0 opacity-[0.02] dark:opacity-[0.03]">
				<div className="absolute top-1/4 left-1/4 w-32 h-32 border border-artistic-red rotate-45 transform-gpu" />
				<div className="absolute bottom-1/3 right-1/3 w-24 h-24 border border-artistic-blue rotate-12 transform-gpu" />
				<div className="absolute top-1/2 right-1/4 w-16 h-16 border border-artistic-yellow rotate-[30deg] transform-gpu" />
			</div>

			<div className="max-w-7xl mx-auto w-full relative z-10">
				<div className="text-center">
					<motion.div
						initial={{ opacity: 0, y: 30 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.8, delay: 0.4 }}
						className="space-y-6"
						style={{
							scale: nameScale,
							opacity: nameOpacity
						}}
					>
						{/* Enhanced name with Persian typography influence */}
						<motion.h1
							className="text-6xl md:text-8xl font-medium tracking-tight"
							style={{ letterSpacing }}
						>
							<motion.span
								className="block"
								initial={{ opacity: 0, x: -20 }}
								animate={{ opacity: 1, x: 0 }}
								transition={{ duration: 0.6, delay: 0.6 }}
							>
								Mehdi
							</motion.span>
							<motion.span
								className="block text-artistic-red relative"
								initial={{ opacity: 0, x: 20 }}
								animate={{ opacity: 1, x: 0 }}
								transition={{ duration: 0.6, delay: 0.8 }}
							>
								Asadi
								{/* Subtle Persian calligraphy-inspired accent */}
								<motion.div
									className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-16 h-px bg-artistic-red"
									initial={{ scaleX: 0 }}
									animate={{ scaleX: 1 }}
									transition={{ duration: 0.8, delay: 1.2 }}
								/>
							</motion.span>
						</motion.h1>

						{/* Enhanced description with cultural context */}
						<motion.p
							className="text-xl md:text-2xl text-muted leading-relaxed max-w-2xl mx-auto"
							initial={{ opacity: 0, y: 20 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ duration: 0.8, delay: 1.0 }}
						>
							Visual artist & researcher exploring the intersection of
							<motion.span
								className="text-artistic-blue font-medium"
								whileHover={{ scale: 1.05 }}
								transition={{ duration: 0.2 }}
							> design, culture,</motion.span> and
							<motion.span
								className="text-artistic-yellow font-medium"
								whileHover={{ scale: 1.05 }}
								transition={{ duration: 0.2 }}
							> human expression</motion.span> in Tehran.
						</motion.p>

						{/* Cultural tagline */}
						<motion.div
							className="text-sm text-muted/60 tracking-wider uppercase"
							initial={{ opacity: 0 }}
							animate={{ opacity: 1 }}
							transition={{ duration: 0.8, delay: 1.4 }}
						>
							Contemporary Persian Visual Language
						</motion.div>
					</motion.div>
				</div>
			</div>
		</section>
	);
}
