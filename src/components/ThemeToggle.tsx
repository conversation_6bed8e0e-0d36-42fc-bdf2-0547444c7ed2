"use client";
import { <PERSON>, Sun } from "lucide-react";
import { useTheme } from "next-themes";

export default function ThemeToggle(): JSX.Element {
  const { theme, setTheme } = useTheme();
  return (
    <button
      onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
      aria-label="Toggle theme"
    >
      {theme === "dark" ? <Sun size={20} /> : <Moon size={20} />}
    </button>
  );
}
