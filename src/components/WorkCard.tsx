"use client";
import Image from "next/image";
import Link from "next/link";
import { motion } from "framer-motion";

interface WorkCardProps {
  slug: string;
  title: string;
  role: string;
  year: string;
  thumb: string;
  description: string;
  index?: number;
}

export default function WorkCard({
  slug,
  title,
  role,
  year,
  thumb,
  description,
  index = 0,
}: WorkCardProps) {
  // Different layouts for visual variety
  const isLarge = index === 0;
  const isWide = index === 1;

  return (
    <Link href={`/work/${slug}`} scroll={false}>
      <motion.div
        whileHover={{
          y: -12,
          scale: 1.02,
          transition: { duration: 0.4, ease: [0.25, 0.46, 0.45, 0.94] }
        }}
        whileTap={{ scale: 0.98 }}
        className="group relative overflow-hidden border border-border hover:border-border-light bg-surface hover:bg-surface-elevated transition-all duration-500 ease-out"
      >
        {/* Image container with enhanced aspect ratios */}
        <div className={`
          relative overflow-hidden
          ${isLarge ? 'aspect-[4/5]' : isWide ? 'aspect-[16/9]' : 'aspect-[3/4]'}
        `}>
          <Image
            src={thumb}
            alt={title}
            fill
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            priority={index === 0}
            quality={90}
            placeholder="blur"
            blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
            className="object-cover group-hover:scale-110 transition-transform duration-1000 ease-out"
          />

          {/* Sophisticated overlay system */}
          <div className="absolute inset-0 bg-gradient-to-t from-background/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
          <div className="absolute inset-0 bg-artistic-red/0 group-hover:bg-artistic-red/8 transition-all duration-500" />

          {/* Enhanced year badge */}
          <motion.div
            initial={{ opacity: 0.8 }}
            whileHover={{ opacity: 1, scale: 1.05 }}
            className="absolute top-4 right-4 bg-surface-elevated/95 backdrop-blur-md px-3 py-1.5 type-caption text-muted border border-border/50"
          >
            {year}
          </motion.div>
        </div>

        {/* Content with enhanced typography system */}
        <div className="p-6 space-y-5">
          <div className="space-y-3">
            <h3 className="type-heading-sm leading-tight group-hover:text-artistic-red transition-colors duration-300">
              {title}
            </h3>

            <p className="type-caption text-muted/80">
              {role}
            </p>
          </div>

          <div className="h-px bg-border w-8 group-hover:w-16 group-hover:bg-artistic-red transition-all duration-500 ease-out" />

          <p className="type-body-sm text-muted/90 leading-relaxed line-clamp-3">
            {description}
          </p>

          {/* Enhanced interaction hint */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            whileHover={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2 }}
            className="flex items-center space-x-2 type-caption text-muted/60 group-hover:text-artistic-red transition-colors duration-300"
          >
            <span>Explore Project</span>
            <motion.span
              animate={{ x: [0, 4, 0] }}
              transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
            >
              →
            </motion.span>
          </motion.div>
        </div>
      </motion.div>
    </Link>
  );
}
