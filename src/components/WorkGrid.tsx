"use client";
import { motion } from "framer-motion";
import works from "@/data/artistic-works.json";
import WorkCard from "./WorkCard";

export default function WorkGrid() {
	return (
		<section id="work" className="py-16 px-4 container mx-auto">
			<div className="text-center mb-12">
				<motion.h2
					initial={{ opacity: 0, y: 30 }}
					whileInView={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.8 }}
					viewport={{ once: true }}
					className="text-5xl md:text-6xl font-medium mb-4"
				>
					Selected Works
				</motion.h2>
				<motion.p
					initial={{ opacity: 0, y: 30 }}
					whileInView={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.8, delay: 0.2 }}
					viewport={{ once: true }}
					className="text-lg text-muted max-w-md mx-auto"
				>
					A curated collection of visual explorations spanning
					editorial design, cultural research, and experimental typography.
				</motion.p>
			</div>

			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
				{works.map((work, index) => (
					<motion.div
						key={work.slug}
						initial={{ opacity: 0, y: 50 }}
						whileInView={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.8, delay: index * 0.1 }}
						viewport={{ once: true }}
					>
						<WorkCard {...work} index={index} />
					</motion.div>
				))}
			</div>
		</section>
	);
}
